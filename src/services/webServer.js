const express = require("express");
const path = require("node:path");
const QRCode = require("qrcode");
const logger = require("../utils/logger");
const { configService } = require("./configService");

class WebServer {
  constructor() {
    this.app = express();
    this.server = null;
    this.qrCodeData = null;
    this.status = "initializing";
    this.port = process.env.WEB_PORT || 3280;

    this.setupRoutes();
  }

  setupRoutes() {
    // Parse JSON bodies
    this.app.use(express.json());

    // Serve static files from public directory
    this.app.use(express.static(path.join(__dirname, "..", "public")));

    // API endpoint to get current status and QR code
    this.app.get("/api/status", (req, res) => {
      res.json({
        status: this.status,
        qrCode: this.qrCodeData,
        timestamp: Date.now(),
      });
    });

    // Health check endpoint
    this.app.get("/api/health", (req, res) => {
      res.json({ status: "ok", uptime: process.uptime() });
    });

    // Configuration endpoints
    this.app.get("/api/config", (req, res) => {
      try {
        const config = configService.getAll();
        const availableProviders = configService.getAvailableProviders();
        const availablePrompts = configService.getAvailablePrompts();

        // Check which API keys are available
        const apiKeys = {
          groq: !!process.env.GROQ_API_KEY,
          gemini: !!process.env.GOOGLE_GENERATIVE_AI_API_KEY,
          openai: !!process.env.OPENAI_API_KEY,
        };

        res.json({
          config,
          availableProviders,
          availablePrompts,
          apiKeys,
          availableModels: configService.getAvailableModels(config.provider),
        });
      } catch (error) {
        logger.error("Error getting configuration:", error);
        res.status(500).json({ error: "Failed to get configuration" });
      }
    });

    this.app.get("/api/config/models/:provider", (req, res) => {
      try {
        const { provider } = req.params;
        const models = configService.getAvailableModels(provider);
        res.json({ models });
      } catch (error) {
        logger.error("Error getting models:", error);
        res.status(500).json({ error: "Failed to get models" });
      }
    });

    this.app.post("/api/config", (req, res) => {
      try {
        const updates = req.body;

        // Validate the configuration
        const errors = configService.validateConfig(updates);
        if (errors.length > 0) {
          return res
            .status(400)
            .json({ error: "Validation failed", details: errors });
        }

        // Update the configuration
        const changes = configService.updateMultiple(updates);

        res.json({
          success: true,
          message: "Configuration updated successfully",
          changes,
        });
      } catch (error) {
        logger.error("Error updating configuration:", error);
        res.status(500).json({ error: "Failed to update configuration" });
      }
    });

    this.app.post("/api/config/reset", (req, res) => {
      try {
        // Reset to default configuration
        const defaultConfig = {
          provider: process.env.MODEL_PROVIDER || "gemini",
          model: configService.getDefaultModel(
            process.env.MODEL_PROVIDER || "gemini"
          ),
          botName: process.env.BOT_NAME || "Nonce",
          rateLimit: Number.parseInt(process.env.RATE_LIMIT || "5"),
          rateLimitWindow: Number.parseInt(
            process.env.RATE_LIMIT_WINDOW || "60000"
          ),
          maxContextMessages: Number.parseInt(
            process.env.MAX_CONTEXT_MESSAGES || "10"
          ),
          groupOnly: process.env.GROUP_ONLY === "true",
          promptTemplate: "turing3",
          customPrompt: "",
          temperature: 1.2,
        };

        const changes = configService.updateMultiple(defaultConfig);

        res.json({
          success: true,
          message: "Configuration reset to defaults",
          changes,
        });
      } catch (error) {
        logger.error("Error resetting configuration:", error);
        res.status(500).json({ error: "Failed to reset configuration" });
      }
    });

    // Root endpoint redirects to index.html
    this.app.get("/", (req, res) => {
      res.sendFile(path.join(__dirname, "..", "public", "index.html"));
    });
  }

  async start() {
    try {
      this.server = this.app.listen(this.port, () => {
        logger.success(`Web server started on port ${this.port}`);
        logger.info(`QR Code page available at: http://localhost:${this.port}`);
      });
    } catch (error) {
      logger.error("Failed to start web server:", error);
      throw error;
    }
  }

  async updateQRCode(qrData) {
    try {
      // Generate QR code as data URL
      this.qrCodeData = await QRCode.toDataURL(qrData, {
        type: "image/png",
        quality: 0.92,
        margin: 1,
        width: 300,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      logger.info("QR code updated for web display");
    } catch (error) {
      logger.error("Failed to generate QR code for web:", error);
    }
  }

  updateStatus(newStatus) {
    this.status = newStatus;
    logger.info(`Web server status updated: ${newStatus}`);
  }

  clearQRCode() {
    this.qrCodeData = null;
  }

  async stop() {
    if (this.server) {
      return new Promise((resolve) => {
        this.server.close(() => {
          logger.info("Web server stopped");
          resolve();
        });
      });
    }
  }
}

module.exports = { WebServer };
